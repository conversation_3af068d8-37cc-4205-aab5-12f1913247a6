package com.pacto.cadastrosAuxiliares.controller;


import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroEmpresaJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.EmpresaService;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.empresa.ExemploRespostaEmpresa;
import com.pacto.cadastrosAuxiliares.swagger.respostas.empresa.ExemploRespostaListEmpresa;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/empresas")
public class EmpresaController {
    private final EmpresaService empresaService;

    public EmpresaController(EmpresaService empresaService) {
        this.empresaService = empresaService;
    }

    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false)JSONObject filtros) {
        try {
            FiltroEmpresaJSON filtroEmpresaJSON = new FiltroEmpresaJSON(filtros);
            return ResponseEntityFactory.ok(empresaService.findAll(filtroEmpresaJSON));
        } catch (ServiceException exception) {
            StringBuilder result = new StringBuilder(exception.toString() + "\n");
            StackTraceElement[] trace = exception.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(exception.getChaveExcecao(), result.toString());
        }
    }

    @GetMapping(value = "/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> findEmpresaById(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(empresaService.findEmpresaById(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
