package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.cadastrosAuxiliares.dto.basico.ImpostoProdutoCfopDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroImpostoProdutoJSON;
import com.pacto.cadastrosAuxiliares.services.interfaces.ImpostoProdutoCfopService;
import com.pacto.cadastrosAuxiliares.swagger.ExemploRespostaVazia;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.impostoproduto.ExemploRespostaConfigNotaFiscal;
import com.pacto.cadastrosAuxiliares.swagger.respostas.impostoproduto.ExemploRespostaImpostoProduto;
import com.pacto.cadastrosAuxiliares.swagger.respostas.impostoproduto.ExemploRespostaListImpostoProdutoPaginacao;
import com.pacto.cadastrosAuxiliares.swagger.respostas.impostoproduto.ExemploRespostaListLogsPaginacao;
import com.pacto.cadastrosAuxiliares.swagger.respostas.impostoproduto.ExemploRespostaQuantidadeProdutos;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/imposto-produto")
public class ImpostoProdutoCfopController {

    private final ImpostoProdutoCfopService service;

    public ImpostoProdutoCfopController(ImpostoProdutoCfopService service) {

        this.service = service;
    }

    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                     PaginadorDTO paginadorDTO , @RequestHeader("empresaId") Integer empresaId) {
        try {
            FiltroImpostoProdutoJSON filtroImpostoProdutoJSON = new FiltroImpostoProdutoJSON(filtros,empresaId);
            return ResponseEntityFactory.ok(service.findAll(filtroImpostoProdutoJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(),  result.toString());
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> imposto(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(service.findImpostoProduto(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> incluir(@RequestBody ImpostoProdutoCfopDTO dto,
                                                       @RequestHeader("empresaId") Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(service.saveOrUpdate(dto, empresaId));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(),  result.toString());
        }

    }

    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> delete(@PathVariable Integer id ) {
        try {
            service.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }
    @GetMapping("/config-notafiscal")
    public ResponseEntity<EnvelopeRespostaDTO> buscaConfigNotaFical(@RequestHeader("empresaId") Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(service.findConfigNotaFiscal(empresaId));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @GetMapping("/quantidade-produtos-atualizados/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> buscaQuatidadeDeProdutosAtualizado(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(service.findQuantidadeDeProdutosAtualizado(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @PostMapping("/atualizar-produtos")
    public ResponseEntity<EnvelopeRespostaDTO> atualizaProdutos(@RequestBody ImpostoProdutoCfopDTO dto,
                                                       @RequestHeader("empresaId") Integer empresaId) {
        try {
            service.atualizaProdutos(dto, empresaId);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(),  result.toString());
        }

    }

    @GetMapping("/logs")
    public ResponseEntity<EnvelopeRespostaDTO> buscarLogs(
                                                          PaginadorDTO paginadorDTO,
                                                          @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                          @RequestHeader("empresaId") Integer empresaId) {
        try {
            FiltroImpostoProdutoJSON filters = new FiltroImpostoProdutoJSON(filtros,empresaId);
            return ResponseEntityFactory.ok(service.buscarLogs( paginadorDTO, filters), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

}
