package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.ConfiguracaoSistemaService;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.configuracaoSistema.ExemploRespostaConfiguracaoSistema;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/configuracao-sistema")
public class ConfiguracaoSistemaController {

    private final ConfiguracaoSistemaService configuracaoSistemaService;

    public ConfiguracaoSistemaController(ConfiguracaoSistemaService configuracaoSistemaService) {
        this.configuracaoSistemaService = configuracaoSistemaService;
    }

    @Operation(
            summary = "Consultar configurações do sistema",
            description = "Consulta as configurações gerais do sistema.",
            tags = {SwaggerTags.CONFIGURACOES_SISTEMA},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaConfiguracaoSistema.class)
                            )
                    )
            }
    )
    @ResponseBody
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> get() {
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.get());
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar configurações completas do sistema",
            description = "Consulta todas as configurações detalhadas do sistema.",
            tags = {SwaggerTags.CONFIGURACOES_SISTEMA},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaConfiguracaoSistema.class)
                            )
                    )
            }
    )
    @ResponseBody
    @GetMapping(value = "completo")
    public ResponseEntity<EnvelopeRespostaDTO> getCompleto() {
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.get());
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
