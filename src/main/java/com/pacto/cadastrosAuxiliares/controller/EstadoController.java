package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.cadastrosAuxiliares.dto.basico.EstadoDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroEstadoJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.EstadoService;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.estado.ExemploRespostaEstado;
import com.pacto.cadastrosAuxiliares.swagger.respostas.estado.ExemploRespostaListEstado;
import com.pacto.cadastrosAuxiliares.swagger.respostas.estado.ExemploRespostaListEstadoPaginacao;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/estados")
public class EstadoController {
    private final EstadoService estadoService;

    public EstadoController(EstadoService estadoService) {
        this.estadoService = estadoService;
    }

    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                     PaginadorDTO paginadorDTO) {
        try {
            FiltroEstadoJSON filtroEstadoJSON = new FiltroEstadoJSON(filtros);
            return ResponseEntityFactory.ok(estadoService.findAll(filtroEstadoJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(),  result.toString());
        }
    }

    @GetMapping("/find-by-pais/{idPais}")
    public ResponseEntity<EnvelopeRespostaDTO> findByIdPais(@RequestParam(value = "filters", required = false) JSONObject filtros, @PathVariable Integer idPais){

        try{
            FiltroEstadoJSON filtroEstadoJSON = new FiltroEstadoJSON((filtros));
            return ResponseEntityFactory.ok(estadoService.findByIdPais(filtroEstadoJSON, idPais));
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        }
    }

    @GetMapping(value = "/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> estado(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(estadoService.estado(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirEstado(@RequestBody EstadoDTO estadoDTO) {
        try {
            return ResponseEntityFactory.ok(estadoService.saveOrUpdate(estadoDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(),  result.toString());
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> deletarPlano(@PathVariable Integer id ) {
        try {
            estadoService.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }

    @GetMapping("/verifica-vinculo-cidade/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> verificaVinculoCidade(@PathVariable Integer id ) {
        try {
            estadoService.existeVinculoComCidade(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }

}
