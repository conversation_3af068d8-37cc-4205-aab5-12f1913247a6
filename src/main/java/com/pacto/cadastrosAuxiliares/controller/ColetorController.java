package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroColetorJSON;
import com.pacto.cadastrosAuxiliares.services.interfaces.ColetorService;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.coletor.ExemploRespostaColetor;
import com.pacto.cadastrosAuxiliares.swagger.respostas.coletor.ExemploRespostaListColetorPaginacao;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/coletores")

public class ColetorController {

    private final ColetorService coletorService;

    public ColetorController(ColetorService coletorService) {
        this.coletorService = coletorService;
    }

    @Operation(
            summary = "Consultar coletores de acesso",
            description = "Consulta os coletores de acesso podendo filtrar os resultados da requisição.",
            tags = {SwaggerTags.COLETOR},
            parameters = {
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pela descrição do coletor de acesso",
                            example = "{\"quicksearchValue\":\"recepção\"}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis na conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n",
                            example = "nome,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListColetorPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping("")
    public ResponseEntity<EnvelopeRespostaDTO> findAll(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                       @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroColetorJSON filtroColetorJSON = new FiltroColetorJSON(filtros);
            return ResponseEntityFactory.ok(coletorService.findAll(filtroColetorJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar coletores de acesso com código e nome",
            description = "Consulta os coletores de acesso retornando uma lista com código e nome formatado (número do terminal + nome do computador), podendo filtrar os resultados da requisição.",
            tags = {SwaggerTags.COLETOR},
            parameters = {
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pelo nome do computador ou número do terminal do coletor de acesso",
                            example = "{\"quicksearchValue\":\"recepção\"}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis na conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n",
                            example = "numeroTerminal,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListColetorPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping("/cod-name")
    public ResponseEntity<EnvelopeRespostaDTO> findAllCodName(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                              @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroColetorJSON filtroColetorJSON = new FiltroColetorJSON(filtros);
            return ResponseEntityFactory.ok(coletorService.findAllCodName(filtroColetorJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar coletor de acesso por número do terminal",
            description = "Consulta as informações de um coletor de acesso pelo número do terminal.",
            tags = {SwaggerTags.COLETOR},
            parameters = {
                    @Parameter(name = "numeroTerminal", description = "Número do terminal do coletor de acesso que será consultado", example = "1", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaColetor.class)
                            )
                    )
            }
    )
    @GetMapping("terminal/{numeroTerminal}")
    public ResponseEntity<EnvelopeRespostaDTO> findById(@PathVariable Integer numeroTerminal) {
        try {
            return ResponseEntityFactory.ok(coletorService.findByNumeroTerminal(numeroTerminal));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
