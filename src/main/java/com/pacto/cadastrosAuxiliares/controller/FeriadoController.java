package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.cadastrosAuxiliares.dto.basico.FeriadoDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroFeriadoJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.FeriadoService;
import com.pacto.cadastrosAuxiliares.swagger.ExemploRespostaVazia;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.feriado.ExemploRespostaFeriado;
import com.pacto.cadastrosAuxiliares.swagger.respostas.feriado.ExemploRespostaListFeriadoPaginacao;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/feriados")
public class FeriadoController {
    private final FeriadoService feriadoService;

    public FeriadoController(FeriadoService feriadoService) {
        this.feriadoService = feriadoService;
    }

    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                     PaginadorDTO paginadorDTO) {
        try {
            FiltroFeriadoJSON filtroFeriadoJSON = new FiltroFeriadoJSON(filtros);
            return ResponseEntityFactory.ok(feriadoService.findAll(filtroFeriadoJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(),  result.toString());
        }
    }

    @GetMapping(value = "/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> feriado(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(feriadoService.feriado(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @GetMapping(value = "/{id}/clonar")
    public ResponseEntity<EnvelopeRespostaDTO> clonarFeriado(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(feriadoService.clonarFeriado(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @PostMapping(value = "/replicar")
    public ResponseEntity<EnvelopeRespostaDTO> replicarFeriado(@RequestBody FeriadoDTO feriado) {
        try {
            feriado.setReplicar(true);
            return ResponseEntityFactory.ok(feriadoService.saveOrUpdate(feriado));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirFeriado(@RequestBody FeriadoDTO feriadoDTO) {
        try {
            return ResponseEntityFactory.ok(feriadoService.saveOrUpdate(feriadoDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(),  result.toString());
        }

    }

    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> deletarFeriado(@PathVariable Integer id ) {
        try {
            feriadoService.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }
}
