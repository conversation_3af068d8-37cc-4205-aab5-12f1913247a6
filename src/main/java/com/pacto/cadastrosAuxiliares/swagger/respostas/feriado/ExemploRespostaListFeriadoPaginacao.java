package com.pacto.cadastrosAuxiliares.swagger.respostas.feriado;

import com.pacto.cadastrosAuxiliares.dto.basico.FeriadoDTO;
import com.pacto.cadastrosAuxiliares.swagger.ExemploPaginadorResposta;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(name = "Exemplo Resposta - Lista de Feriados Paginada", description = "Exemplo da resposta contendo uma lista paginada de feriados")
public class ExemploRespostaListFeriadoPaginacao extends ExemploPaginadorResposta {

    @Schema(description = "Lista de feriados retornada na consulta")
    private List<FeriadoDTO> content;

    public List<FeriadoDTO> getContent() {
        return content;
    }

    public void setContent(List<FeriadoDTO> content) {
        this.content = content;
    }
}
